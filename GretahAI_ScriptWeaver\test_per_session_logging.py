#!/usr/bin/env python3
"""
Test script for per-session logging functionality.

This script validates that the per-session logging implementation is working correctly.
"""

import sys
import os
from pathlib import Path

def test_per_session_logging():
    """Test the per-session logging functionality."""
    print("🧪 Testing GretahAI ScriptWeaver Per-Session Logging")
    print("=" * 60)
    
    try:
        # Test 1: Import and initialize logging manager
        print("Test 1: Importing logging manager...")
        from core.logging_config import GretahLoggingManager, get_session_id, get_current_log_file
        print("✅ Successfully imported logging components")
        
        # Test 2: Get session information
        print("\nTest 2: Getting session information...")
        session_id = get_session_id()
        log_file = get_current_log_file()
        print(f"✅ Session ID: {session_id}")
        print(f"✅ Log file: {log_file}")
        
        # Test 3: Check if log file exists
        print("\nTest 3: Checking log file existence...")
        log_path = Path(log_file)
        if log_path.exists():
            print(f"✅ Log file exists: {log_path}")
            
            # Read and display log file contents
            with open(log_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                print(f"✅ Log file has {len(lines)} lines")
                
                # Check for session start marker
                if "Session Started" in content:
                    print("✅ Session start marker found in log")
                else:
                    print("⚠️ Session start marker not found in log")
        else:
            print(f"❌ Log file does not exist: {log_path}")
        
        # Test 4: Check session log file pattern
        print("\nTest 4: Checking session log file pattern...")
        logs_dir = Path("logs")
        if logs_dir.exists():
            session_logs = list(logs_dir.glob("scriptweaver_????????_??????.log"))
            print(f"✅ Found {len(session_logs)} session log files:")
            for log in session_logs[-5:]:  # Show last 5 files
                print(f"   - {log.name}")
        else:
            print("❌ Logs directory does not exist")
        
        # Test 5: Test debug utilities
        print("\nTest 5: Testing debug utilities...")
        try:
            from debug_utils import get_session_info
            session_info = get_session_info()
            print("✅ Successfully got session info:")
            for key, value in session_info.items():
                print(f"   {key}: {value}")
        except Exception as e:
            print(f"⚠️ Debug utilities test failed: {e}")
        
        print("\n" + "=" * 60)
        print("🎉 Per-session logging test completed!")
        print("✅ All core functionality is working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_per_session_logging()
    sys.exit(0 if success else 1)
